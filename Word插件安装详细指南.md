# Word智能续写插件安装详细指南

## 🎯 安装前准备

### 1. 确保服务器运行
```bash
cd d:\WordGpt
npm start
```
确认看到：`服务器运行在 http://localhost:3000`

### 2. 检查文件
确认项目文件夹中有 `manifest.xml` 文件

## 📋 安装方法（按优先级排序）

### 方法一：Office 365 / Word 2019+

1. **打开Word**
2. **点击"插入"选项卡**
3. **查找以下选项之一**：
   - "获取加载项"
   - "Office 加载项"
   - "我的加载项"
4. **在弹出窗口中查找**：
   - "上传我的加载项"
   - "管理我的加载项"
   - "从文件上传"
5. **选择manifest.xml文件**
   - 浏览到 `d:\WordGpt\manifest.xml`
   - 点击"上传"

### 方法二：通过开发者工具

1. **启用开发者选项卡**
   - 文件 → 选项 → 自定义功能区
   - 勾选"开发工具"
   - 点击确定

2. **使用开发工具**
   - 点击"开发工具"选项卡
   - 选择"加载项"或"COM加载项"
   - 添加新的加载项

### 方法三：直接双击安装

1. **在文件资源管理器中**
   - 导航到 `d:\WordGpt`
   - 双击 `manifest.xml` 文件
   - 选择用Word打开
   - Word可能会自动识别并安装插件

### 方法四：Web版Word测试

如果桌面版有问题，可以先在Web版测试：

1. **打开 office.com**
2. **登录你的Microsoft账户**
3. **打开Word Online**
4. **插入 → 加载项 → 上传我的加载项**

## 🔧 不同Word版本的界面差异

### Office 365 (最新版)
- 插入 → 获取加载项 → 上传我的加载项

### Word 2019
- 插入 → Office 加载项 → 管理我的加载项

### Word 2016
- 插入 → 我的加载项 → 上传我的加载项

### Word Online
- 插入 → 加载项 → 上传我的加载项

## ⚠️ 常见问题及解决方案

### 问题1：找不到"上传我的加载项"选项

**解决方案A**：
1. 确保你使用的是Office 365或Word 2016+
2. 检查是否有管理员权限
3. 尝试重启Word

**解决方案B**：
1. 使用Web版Word进行测试
2. 联系IT管理员检查组织策略

### 问题2：上传后插件不显示

**解决方案**：
1. 检查服务器是否运行在 http://localhost:3000
2. 确认防火墙没有阻止本地服务器
3. 尝试刷新Word或重启

### 问题3：插件加载失败

**解决方案**：
1. 检查manifest.xml文件是否完整
2. 确认所有URL指向正确的本地服务器
3. 查看浏览器开发者工具的错误信息

## 🎯 验证安装成功

### 1. 查看插件按钮
安装成功后，在Word的"主页"选项卡中应该出现"智能续写"按钮

### 2. 测试任务面板
- 点击"智能续写"按钮
- 右侧应该出现任务面板
- 面板中包含参数设置和"生成续写"按钮

### 3. 测试功能
- 在文档中输入一些文字
- 点击"生成续写"
- 应该能看到Qwen生成的续写内容

## 🔍 调试技巧

### 1. 开发者工具
- 在任务面板中右键 → 检查元素
- 查看控制台是否有错误信息

### 2. 网络检查
- 确认能访问 http://localhost:3000/taskpane.html
- 检查API调用是否成功

### 3. 服务器日志
- 查看运行npm start的终端输出
- 观察是否有错误信息

## 📞 获取帮助

如果以上方法都不行，请提供以下信息：

1. **Word版本**：帮助 → 关于Microsoft Word
2. **操作系统**：Windows版本
3. **错误信息**：具体的错误提示
4. **截图**：插入选项卡的界面截图

## 🚀 快速安装脚本

运行项目文件夹中的 `install-plugin.bat` 文件，它会显示详细的安装步骤。

---

**提示**：如果你的组织有IT策略限制，可能需要联系管理员来安装自定义插件。
