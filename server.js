const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const axios = require('axios');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// 存储知识库数据（实际项目中应使用数据库）
let knowledgeBase = [];

// Qwen API配置
const QWEN_API_KEY = process.env.QWEN_API_KEY || 'your-qwen-api-key';
const QWEN_API_URL = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

// 路由：获取文本补全
app.post('/api/complete', async (req, res) => {
  try {
    const { text, maxTokens = 150, temperature = 0.7 } = req.body;

    if (!text) {
      return res.status(400).json({ error: '缺少文本内容' });
    }

    // 构建知识库上下文
    let knowledgeContext = '';
    if (knowledgeBase.length > 0) {
      knowledgeContext = '\n\n相关知识库内容：\n' +
        knowledgeBase.map(kb => `- ${kb.title}: ${kb.content}`).join('\n');
    }

    const prompt = `请根据以下文本内容，自然地续写下一段内容。续写应该保持文档的风格和逻辑连贯性。${knowledgeContext}

当前文档内容：
${text}

续写内容：`;

    const response = await axios.post(QWEN_API_URL, {
      model: 'qwen-turbo',
      input: {
        messages: [
          {
            role: 'system',
            content: '你是一个专业的写作助手，擅长根据上下文续写文档内容。请保持文档的风格和逻辑连贯性，续写内容应该自然流畅。'
          },
          {
            role: 'user',
            content: prompt
          }
        ]
      },
      parameters: {
        max_tokens: maxTokens,
        temperature: temperature,
        stream: false
      }
    }, {
      headers: {
        'Authorization': `Bearer ${QWEN_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const completion = response.data.output.text.trim();

    res.json({
      success: true,
      completion: completion,
      usage: response.data.usage
    });

  } catch (error) {
    console.error('API调用错误:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: '文本补全失败',
      details: error.response?.data?.error?.message || error.message
    });
  }
});

// 路由：获取知识库
app.get('/api/knowledge', (req, res) => {
  res.json({
    success: true,
    data: knowledgeBase
  });
});

// 路由：添加知识库条目
app.post('/api/knowledge', (req, res) => {
  const { title, content } = req.body;

  if (!title || !content) {
    return res.status(400).json({ error: '标题和内容不能为空' });
  }

  const newEntry = {
    id: Date.now().toString(),
    title,
    content,
    createdAt: new Date().toISOString()
  };

  knowledgeBase.push(newEntry);

  res.json({
    success: true,
    data: newEntry
  });
});

// 路由：删除知识库条目
app.delete('/api/knowledge/:id', (req, res) => {
  const { id } = req.params;
  const index = knowledgeBase.findIndex(kb => kb.id === id);

  if (index === -1) {
    return res.status(404).json({ error: '知识库条目不存在' });
  }

  knowledgeBase.splice(index, 1);

  res.json({
    success: true,
    message: '删除成功'
  });
});

// 静态文件路由
app.get('/taskpane.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'taskpane.html'));
});

app.get('/commands.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'commands.html'));
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log('请确保已设置 QWEN_API_KEY 环境变量');
});

module.exports = app;
