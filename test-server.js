// 简单的服务器测试脚本
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testServer() {
  console.log('开始测试服务器...\n');

  try {
    // 测试知识库API
    console.log('1. 测试添加知识库条目...');
    const addResponse = await axios.post(`${API_BASE_URL}/knowledge`, {
      title: '测试知识',
      content: '这是一个测试知识库条目，用于验证API功能。'
    });
    console.log('✓ 添加知识库成功:', addResponse.data);

    // 测试获取知识库
    console.log('\n2. 测试获取知识库...');
    const getResponse = await axios.get(`${API_BASE_URL}/knowledge`);
    console.log('✓ 获取知识库成功:', getResponse.data);

    // 测试文本补全（需要API密钥）
    console.log('\n3. 测试文本补全...');
    try {
      const completeResponse = await axios.post(`${API_BASE_URL}/complete`, {
        text: '今天是个好天气',
        maxTokens: 50,
        temperature: 0.7
      });
      console.log('✓ 文本补全成功:', completeResponse.data);
    } catch (error) {
      if (error.response?.status === 500) {
        console.log('⚠ 文本补全失败 - 可能需要配置Qwen API密钥');
        console.log('错误信息:', error.response.data.error);
      } else {
        throw error;
      }
    }

    console.log('\n✓ 服务器测试完成！');

  } catch (error) {
    console.error('✗ 测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('请确保服务器正在运行 (npm start)');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testServer();
}

module.exports = testServer;
