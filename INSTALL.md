# Word智能续写插件安装指南

## 快速开始

### 1. 准备工作

确保你的系统已安装：
- Node.js (版本 14 或更高)
- Microsoft Word (Office 365 或 2019+)
- 有效的 OpenAI API 密钥

### 2. 项目设置

1. **克隆或下载项目**
   ```bash
   # 如果使用git
   git clone <repository-url>
   cd WordGpt
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑 .env 文件，添加你的 OpenAI API 密钥
   # OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

4. **启动服务器**
   ```bash
   npm start
   ```
   
   服务器将在 http://localhost:3000 启动

### 3. 安装Word插件

#### 方法一：通过开发者模式（推荐用于开发和测试）

1. **启用开发者模式**
   - 打开 Word
   - 转到 **文件** > **选项** > **信任中心** > **信任中心设置**
   - 选择 **受信任的加载项目录**
   - 添加项目根目录路径（包含 manifest.xml 的文件夹）

2. **加载插件**
   - 在 Word 中，转到 **插入** > **我的加载项**
   - 选择 **共享文件夹**
   - 找到并选择 "智能续写助手"
   - 点击 **添加**

#### 方法二：通过上传清单文件

1. **打开Word**
2. **转到插入选项卡**
   - 点击 **插入** > **获取加载项**
3. **上传清单**
   - 选择 **上传我的加载项**
   - 浏览并选择项目根目录下的 `manifest.xml` 文件
   - 点击 **上传**

### 4. 使用插件

1. **打开任务面板**
   - 在Word工具栏的 **主页** 选项卡中找到 **智能续写** 按钮
   - 点击按钮打开任务面板

2. **开始使用**
   - 在文档中输入一些文字
   - 在任务面板中点击 **生成续写**
   - 查看生成的建议并选择插入

## 故障排除

### 常见问题

1. **插件无法加载**
   - 确保服务器正在运行 (http://localhost:3000)
   - 检查 manifest.xml 中的URL是否正确
   - 确保Word信任了插件目录

2. **API调用失败**
   - 检查 .env 文件中的 OPENAI_API_KEY 是否正确
   - 确保网络连接正常
   - 检查API密钥是否有足够的额度

3. **任务面板空白**
   - 按F12打开开发者工具查看错误
   - 检查控制台是否有JavaScript错误
   - 确保所有文件路径正确

### 调试模式

1. **启用开发者工具**
   - 在任务面板中右键选择 **检查元素**
   - 查看控制台输出和网络请求

2. **服务器日志**
   - 查看运行 `npm start` 的终端输出
   - 检查API请求和响应

### 生产部署

如果要在生产环境中使用：

1. **部署到服务器**
   - 将代码部署到支持HTTPS的服务器
   - 更新 manifest.xml 中的所有URL为实际服务器地址

2. **更新配置**
   ```xml
   <!-- 将所有 localhost:3000 替换为实际域名 -->
   <SourceLocation DefaultValue="https://yourdomain.com/taskpane.html"/>
   ```

3. **SSL证书**
   - 确保服务器配置了有效的SSL证书
   - Office插件在生产环境中要求HTTPS

## 技术支持

如果遇到问题：

1. 检查 README.md 中的详细说明
2. 查看项目的 Issues 页面
3. 运行测试脚本：`node test-server.js`

## 开发说明

### 修改插件

- 修改 `taskpane.html`、`taskpane.js`、`taskpane.css` 来自定义界面
- 修改 `server.js` 来调整API逻辑
- 修改 `manifest.xml` 来更改插件配置

### 热重载

使用开发模式启动服务器：
```bash
npm run dev
```

这将启用自动重启功能，当文件发生变化时服务器会自动重启。
