# Word智能续写插件

基于Qwen大模型的Word插件，提供智能文档续写功能。

## 功能特性

- **智能续写**: 根据文档上下文自动生成续写内容
- **知识库管理**: 创建和管理知识库，为续写提供参考
- **可配置参数**: 支持调整上下文长度、生成长度和创意程度
- **一键插入**: 将生成的内容直接插入到Word文档中

## 技术栈

- **前端**: Office Add-in (HTML/CSS/JavaScript)
- **后端**: Node.js + Express
- **AI服务**: 阿里云Qwen大模型API

## 安装和使用

### 1. 环境准备

确保已安装 Node.js (版本 14 或更高)

### 2. 安装依赖

```bash
npm install
```

### 3. 配置API密钥

复制 `.env.example` 为 `.env` 并填入你的 Qwen API 密钥：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```
QWEN_API_KEY=your-actual-qwen-api-key
PORT=3000
```

### 4. 启动服务器

```bash
npm start
```

或使用开发模式（自动重启）：
```bash
npm run dev
```

### 5. 安装Word插件

1. 打开 Microsoft Word
2. 转到 **插入** > **获取加载项**
3. 选择 **上传我的加载项**
4. 选择项目根目录下的 `manifest.xml` 文件
5. 插件将出现在Word的 **主页** 选项卡中

## 使用说明

### 智能续写

1. 在Word文档中输入一些文字作为上下文
2. 点击Word工具栏中的 **智能续写** 按钮打开任务面板
3. 调整参数（可选）：
   - **上下文长度**: 用于分析的文档末尾字符数
   - **生成长度**: 续写内容的长度
   - **创意程度**: 控制生成内容的创新性
4. 点击 **生成续写** 按钮
5. 查看生成的续写建议
6. 点击 **插入到文档** 将内容添加到文档中

### 知识库管理

1. 在任务面板的知识库部分添加相关知识条目
2. 输入标题和内容
3. 点击 **添加知识** 按钮
4. 知识库内容会在续写时作为参考上下文

## API接口

### POST /api/complete
生成文本补全

**请求体:**
```json
{
  "text": "文档内容",
  "maxTokens": 200,
  "temperature": 0.7
}
```

**响应:**
```json
{
  "success": true,
  "completion": "生成的续写内容",
  "usage": {...}
}
```

### GET /api/knowledge
获取知识库列表

### POST /api/knowledge
添加知识库条目

### DELETE /api/knowledge/:id
删除知识库条目

## 开发说明

### 项目结构

```
├── manifest.xml          # Word插件配置文件
├── server.js             # Express服务器
├── taskpane.html         # 任务面板HTML
├── taskpane.js           # 任务面板JavaScript
├── taskpane.css          # 任务面板样式
├── commands.html         # 命令处理页面
├── package.json          # 项目配置
└── README.md            # 说明文档
```

### 调试

1. 在Word中按 F12 打开开发者工具
2. 在任务面板中右键选择 **检查元素**
3. 查看控制台输出和网络请求

### 部署

1. 将代码部署到服务器
2. 更新 `manifest.xml` 中的URL为实际服务器地址
3. 确保HTTPS配置正确（生产环境必需）

## 注意事项

- 需要有效的阿里云Qwen API密钥
- 插件需要网络连接才能正常工作
- 建议在生产环境中使用HTTPS
- API调用会产生费用，请注意使用量

## 许可证

MIT License
